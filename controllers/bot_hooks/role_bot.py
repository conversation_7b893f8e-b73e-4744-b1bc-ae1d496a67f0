from datetime import <PERSON><PERSON><PERSON>
import json
import logging
import os
from aiogram import Bo<PERSON>, types, F
from aiogram.enums import ParseMode
from aiogram.filters import CommandStart, CommandObject, Command
from aiogram.types import (
    CallbackQuery,
    Message,
)
from aiogram.types.bot_command import <PERSON>t<PERSON>ommand
from aiogram.utils.deep_linking import decode_payload
from aiogram.fsm.context import FSMContext
from aiogram.utils.keyboard import InlineKeyboardBuilder
from fastapi import Header
from fastapi.routing import APIRouter
from dotenv import load_dotenv
from common.common_constant import (
    BotSettingType,
    ErrorKey,
    Language,
    PopupPosition,
    RoleTag,
)
from common.bot_common import (
    <PERSON>ton,
    CheckInCallback,
    DiamondSeasonRewardCallback,
    MessageTemplate,
    ModelAutoChangeCallback,
    RechargeMenuPopupCallback,
)
from controllers.bot_hooks import bot_cmd
from controllers.bot_hooks import chat_roles
from controllers.bot_hooks.bot_services import (
    bot_chat_history,
    bot_deeplink,
    bot_model_switch,
    bot_operation,
)
from controllers.bot_hooks.bot_tasks import (
    send_welfare_tip,
    try_send_tasks,
    help_tip,
)
from controllers.bot_hooks.charge_chat import (
    callback_recharge,
    command_recharge,
    handle_successful_payment,
)
from controllers.bot_hooks.chat_roles import (
    get_role_list,
    send_activity_role_card,
    send_role,
)
from controllers.bot_hooks.zbot_chat import (
    atomic_do_chat,
    reset_chat,
)
from controllers.user_check import user_service
from persistence import redis_client
from persistence.models.models import (
    User,
    UserStatus,
)
from common.bot_common import charge_url
from services import (
    check_in_service,
    gift_award_service,
    operation_service,
    package_voucher_service,
    re_purchase_service,
    tg_config_service,
    tg_message_service,
    user_active_service,
    user_diamond_season_service,
    user_growth_service,
)
from services.account_service import AccountService
from services.user import user_benefit_service
from services.user_service import update_user_premium_status
from utils import (
    user_growth_constants,
)
from utils.translate_util import _t, _tl
from .bot_setting import (
    ArchivedRoleSelectCallback,
    ArchivedRoleSlideCallback,
    BotSetting,
    NewRoleSlideCallback,
    RoleListPageCallback,
    RoleSelectCallback,
    VoucherStateGroup,
    router,
    dp,
    send_login_link,
    start_new_chat,
)
from services.bot_services import (
    chat_bots_map as bots,
    chat_bot,
    get_register_source_by_bot,
    get_register_source_by_bot,
    send_go_tma_tip,
    send_invite_notice,
    send_transfer_message,
    send_unlock_tip,
    send_invitation_message,
)

from controllers.bot_hooks import bot_setting

# 这个类是所有role_chat_bot的入口，尽量避免复杂逻辑，将逻辑抽象放到底层中处理
## 枚举类型放到，constants_commen中
## bean类型 放到 bot_common中
## 通用的bot操作，可以放到bot_setting中(setting不依赖业务)

seo_special_role = 177

load_dotenv()

role_bot_router = APIRouter()

WEBHOOK_SECRET = os.environ["TG_WEBHOOK_SECRET"]
BASE_WEBHOOK_URL = os.environ["TG_WEBHOOK_URL"]


log = logging.getLogger(__name__)


# start param {'role_id': 123, 'inviter': 456}
@router.message(CommandStart())
async def command_start_handler(
    message: Message, command: CommandObject, bot: Bot, state: FSMContext, language: str
) -> None:
    logging.info(f"command_start_handler args: {command.args}")
    tg_id = message.from_user.id
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    params = {}
    is_check_in = command.args is not None and command.args.startswith("ct")
    is_archive_related = command.args is not None and (
        command.args.startswith("archive_") or command.args.startswith("role_archive_")
    )
    is_role_share = command.args is not None and command.args.startswith("roleshare_")
    is_act_enroll = command.args is not None and command.args.startswith("act_enroll_")
    if (
        command.args is not None
        and not command.args.startswith("web")
        and not is_check_in
        and not is_archive_related
        and not is_role_share
        and not is_act_enroll
    ):
        if command.args.startswith("u_") or command.args.startswith("r_"):
            for arg in command.args.split("-"):
                if arg.startswith("u_"):
                    uid = arg[2:]
                    if uid.startswith("0x"):
                        uid = int(uid, 16)
                    else:
                        uid = int(uid)
                    params["inviter"] = uid
                elif arg.startswith("r_"):
                    params["role_id"] = int(arg[2:])
        else:
            params = json.loads(decode_payload(command.args))
    is_invitation = False
    inviter_user_id = None
    role_id = params.get("role_id", "0")
    new_user = False
    register_source = get_register_source_by_bot(bot)
    if user is None:
        if "inviter" in params:
            inviter_user_id = params["inviter"]
            await user_service.register_tg_with_invitation(
                message.from_user.id,
                message.from_user.first_name,
                message.from_user.last_name,
                message.from_user.username,
                inviter_user_id,
                register_source,
                message.chat.id,
                role_id,
                message.from_user.is_premium or False,
                bot.id,
            )
            is_invitation = True
        else:
            await user_service.register_by_tg_with_start_role(
                message.from_user.id,
                message.from_user.first_name,
                message.from_user.last_name,
                message.from_user.username,
                message.chat.id,
                register_source,
                role_id,
                message.from_user.is_premium or False,
                bot.id,
            )
        user = await user_service.get_user_by_tg_id(message.from_user.id)
        award = gift_award_service.get_gift_award()
        await gift_award_service.add_award_by_user_gift(user.id, award)
        new_user = True
        await user_growth_service.forward_register(
            user.id, message.from_user.id, inviter_user_id
        )
        # await user_growth_service.new_recharge_product(tg_id,bot)
    if not user:
        return
    await operation_service.send_bot_popup(
        bot, user, message.from_user.id, PopupPosition.CHAT_BOT_START
    )
    if command.args is not None and command.args.startswith("web"):
        await send_login_link(message.from_user.id, bot, language)
        await state.set_state(None)
        return
    if role_id and role_id != "0":
        await state.update_data({"role_id": role_id})
        await start_new_chat(
            message.chat.id, bot, state, user, message.from_user.language_code
        )
    elif (
        not is_check_in
        and not is_archive_related
        and not is_role_share
        and not is_act_enroll
    ):
        await get_role_list(message, bot, state)

    if new_user:
        await try_send_tasks(bot, message.chat.id, user, role_id, True)
        await send_go_tma_tip(bot, message.chat.id)
        # await bot.send_message(message.chat.id, new_user_tip)

    if is_archive_related:
        # 处理/archives 命令的交互中出现的deeplink
        await bot_deeplink.handle_archive_deeplink(command.args, message, bot, state)
    if is_role_share:
        # 处理角色卡分享功能中出现的deeplink
        await bot_deeplink.handle_role_share_deeplink(command.args, message, bot, state)
    if is_act_enroll:
        # 处理「聊天返钻石活动」的报名deeplink
        await bot_deeplink.handle__diamond_season_enroll_deeplink(
            command.args, message, bot, state
        )
    await state.set_state(None)
    if is_check_in:
        await check_in_service.process_in_bot_check_in(message, bot)

    if inviter_user_id is not None:
        await send_invite_notice(message, inviter_user_id)
    if new_user and inviter_user_id:
        await bot_operation.new_user_guide_check(inviter_user_id, state)


@router.message(Command(BotCommand(command="balance", description="查询余额")))
async def handle_balance(message: Message, bot: Bot, state: FSMContext):
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        return
    await bot_cmd.balance(user, message.from_user.id, bot)
    await message.delete()


@router.message(Command(BotCommand(command="free_benefit", description="领取免费权益")))
async def command_free_benefit_handler(message: Message, bot: Bot) -> None:
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        answer_msg = await message.answer("请先注册，再领取免费权益")
        await tg_message_service.del_msg(
            answer_msg.message_id, bot.id, message.from_user.id
        )
        return
    await bot_cmd.free_benefit(user, message.from_user.id, bot)


@router.message(Command(BotCommand(command="recharge", description="充值")))
async def command_recharge_link_handler(
    message: Message, bot: Bot, state: FSMContext
) -> None:
    await command_recharge(message, bot, state)
    # gfk = redis_client.get('bot_charge_sdfkw')
    # if gfk:
    #     await message.answer(chat_bot_charge_tip(), parse_mode=ParseMode.HTML,
    #                     reply_markup=InlineKeyboardMarkup(inline_keyboard=[
    #                         [InlineKeyboardButton(text="立即充值", url=charge_url)]
    #                     ]))
    # else:
    #     builder = InlineKeyboardBuilder()
    #     builder.button(text='立即充值',
    #          url='https://t.me/FancyTavernBot/tavern?startapp=e_eyJwIjoicGF5In0')
    #     await message.answer(go_tma_charge_tip(), reply_markup=builder.as_markup())
    # await state.set_state(None)
    # await send_transfer_message(message, bot)


# 处理弹出recharge menu的inline button
@router.callback_query(RechargeMenuPopupCallback.filter())
async def handle_recharge_menu_pop_up(
    query: CallbackQuery,
    callback_data: ModelAutoChangeCallback,
    bot: Bot,
    state: FSMContext,
):
    await callback_recharge(query, bot, state)


@router.message(Command("list"))
async def handle_list(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    await get_role_list(message, bot, state)
    await state.set_state(None)


@router.message(Command(BotCommand(command="settings", description="设置")))
async def handle_settings(
    message: types.Message,
    command: CommandObject,
    bot: Bot,
    state: FSMContext,
    language: str = Language.ZH.value,
) -> None:
    tg_id = message.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    if not user:
        await message.answer("请先注册，再设置")
        return
    await bot_model_switch.send_settings(user, tg_id, 0, bot, None, state, language)
    await state.set_state(None)
    await message.delete()


@router.callback_query(BotSetting.filter())
async def handle_bot_setting(
    query: CallbackQuery,
    callback_data: BotSetting,
    bot: Bot,
    state: FSMContext,
    language: str,
):
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    if not user:
        log.error(f"User not found: {query.from_user.id}")
        return
    log.info(f"handle_bot_setting,user_id: {user.id}, callback_data: {callback_data}")
    await bot_model_switch.send_settings(
        user,
        query.message.chat.id,
        query.message.message_id,
        bot,
        callback_data,
        state,
        language,
    )
    # 模型做了选择，则结束新手引导
    state_model = await bot_setting.get_state(state)
    if (
        state_model.new_user_guide
        and callback_data.type
        in [BotSettingType.MODEL.value, BotSettingType.CHAT_CHANNEL.value, BotSettingType.MODEL_V1.value]
        and callback_data.select
    ):
        await bot_setting.update_state_new_user_guide(state, False)


@router.message(Command("send_role"))
async def handle_send_role(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    await send_role(message, command, bot, state)
    await state.set_state(None)


@router.message(Command("reset"))
async def handle_reset(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    await reset_chat(message, command, bot, state)
    await state.set_state(None)


@router.message(Command("voucher"))
async def handle_voucher(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    await state.set_state(VoucherStateGroup.voucher)
    await message.answer(
        f'<b>在消息中直接输入卡密，发送给我，可完成卡密兑换💎</b>\n\n如果你还未购买卡密，请直接消息中输入 `/recharge` ，或在"输入消息"左侧点击菜单选择"充值"，去购买卡密'
    )


@router.message(Command("checkin"))
async def handle_check_in(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    await check_in_service.process_in_bot_check_in(message, bot)
    await message.delete()


@router.message(Command("group"))
async def handle_group(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    builder = InlineKeyboardBuilder()
    builder.button(text="加入聊天群", url=user_growth_constants.TARGET_JOIN_GROUP_LINK)
    await message.answer(
        f"点击链接或者按钮加群\n\n{user_growth_constants.TARGET_JOIN_GROUP_LINK}",
        reply_markup=builder.as_markup(),
    )


@router.message(Command("channel"))
async def handle_channel(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    builder = InlineKeyboardBuilder()
    builder.button(text="订阅频道", url=user_growth_constants.ROLE_CHANNEL_LINK)
    await message.answer(
        f"点击链接或者按钮订阅频道\n\n{user_growth_constants.ROLE_CHANNEL_LINK}",
        reply_markup=builder.as_markup(),
    )


@router.message(Command("invite_link"))
async def handle_invite(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    user: User = await user_service.get_user_by_tg_id(message.from_user.id)  # type: ignore
    await send_invitation_message(message, user, bot)


@router.message(Command("welfare"))
async def handle_welfare(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    await send_welfare_tip(bot, message)


@router.message(Command("help"))
async def handle_help_message(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    help_tip_text = await help_tip()
    sent_message = await bot.send_message(message.chat.id, help_tip_text)
    # reg_source = get_register_source_by_bot(bot)
    await tg_message_service.add_check_in_deleted_message(
        message,
        sent_message,
        message.from_user.id,
        timedelta(minutes=10),
        "CHAT_BOT",
        bot.id,
    )


@router.message(Command("unlock"))
async def unlock_help(message: types.Message, bot: Bot):
    await send_unlock_tip(bot, message)


@router.message(Command("advanced"))
async def handle_advanced(message: types.Message, command: CommandObject, bot: Bot):
    builder = InlineKeyboardBuilder()
    info = await bot.get_me()
    url = f"https://t.me/{info.username}/tavern"
    builder.button(text="打开高级版", url=url)
    await bot.send_message(
        message.chat.id, "点击按钮打开高级版", reply_markup=builder.as_markup()
    )


@router.message(Command("hmai"))
async def handle_hmai(
    message: types.Message, command: CommandObject, bot: Bot, language: str
):
    builder = InlineKeyboardBuilder()
    info = await bot.get_me()
    url = f"https://t.me/{info.username}/tavern"
    text = _tl("点击下方按钮打开小程序", language)
    btn_text = _tl("打开小程序", language)

    builder.button(text=btn_text, url=url)
    await bot.send_message(message.chat.id, text, reply_markup=builder.as_markup())


@router.message(VoucherStateGroup.voucher)
async def handle_voucher_message(message: types.Message, bot: Bot, state: FSMContext):
    logging.info(f"handle package voucher message: {message.text}")
    voucher_code = message.text
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    if not user:
        await message.answer("请先注册，再兑换卡密")
        return
    if not voucher_code:
        await message.answer("卡密不能为空，请重新输入")
        return
    voucher = await package_voucher_service.get_package_voucher(voucher_code)
    if voucher is not None and voucher.distributor == "OP_LOTTERY":
        today_free_voucher_orders = (
            await package_voucher_service.get_today_lottery_voucher(user.id)
        )
        if len(today_free_voucher_orders) >= 1:
            await message.answer("今日已经兑换过一次了")
            return

    result, msg, recharge_order = await package_voucher_service.redeem_package_voucher(
        voucher_code, user.id, "CHAT_BOT"
    )
    if result:
        balance = await AccountService.get_total_balance(user.id)
        await message.answer(f"兑换成功，您的余额是 {balance} 💎")
        await user_growth_service.add_fc_reward(user)
        await re_purchase_service.after_recharge(user, recharge_order)
    else:
        await message.answer(f"兑换失败，{msg}")


@router.message(F.successful_payment)
async def on_successful_payment(message: Message, bot: Bot, state: FSMContext):
    await handle_successful_payment(message, bot, state)


@router.message(~F.text.startswith("/"))
async def handle_message(message: types.Message, bot: Bot, state: FSMContext):
    if not message.from_user:
        log.error("message.from_user is None")
        return
    tg_user_id = message.from_user.id
    if message.from_user and message.from_user.is_premium is not None:
        await update_user_premium_status(
            message.from_user.id, message.from_user.is_premium
        )
    if not message or not message.text:
        log.warning(f"Handle message with no text,tg_user_id: {tg_user_id}")
        return

    user = await user_service.get_user_by_tg_id(tg_user_id=tg_user_id)
    if not user:
        log.error("User not found for tg_user_id: %s", tg_user_id)
        await message.answer("用户信息有误，请稍后尝试（或者联系客服）")
        return
    if user.status == UserStatus.CHAT_BLACK.value:
        error = _t(ErrorKey.USER_IN_BLACKLIST.value, Language.ZH.value)
        await message.answer(error)
        return
    lock_key = "tg_chat_message_lock"
    lock_id = f"{tg_user_id}:{message.message_id}"
    if not redis_client.acquire_lock(lock_key, lock_id, 5 * 60, False):
        log.warning(f"repeat message, tg_uid: {tg_user_id},uid: {user.id}")
        return
    if await check_in_service.process_direct_message_check_in(message, bot):
        return
    await operation_service.send_bot_popup(
        bot, user, message.from_user.id, PopupPosition.USER_INPUT
    )
    # 新用户福利弹框 渠道用户：自动领取，非渠道用户：手动领取
    await bot_operation.new_user_benefit_popup(bot, tg_user_id, state)

    # 未切换过通道的，提示通道
    if await bot_operation.channel_switch_guide(bot, tg_user_id):
        return

    chat_id = message.chat.id
    text = message.text
    await atomic_do_chat(
        bot,
        chat_id,
        user,
        text,
        state,
    )


@router.callback_query(CheckInCallback.filter())
async def handle_recharge(
    query: CallbackQuery, bot: Bot, callback_data: CheckInCallback
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await check_in_service.process_in_bot_check_in(query.message, bot, query.from_user)


@router.message(Command(BotCommand(command="archives", description="聊天存档")))
async def handle_archives(message: Message, bot: Bot, state: FSMContext) -> None:
    offset = 0
    limit = 12
    await bot_chat_history.send_archive_roles(
        message.from_user.id, message, state, offset, limit
    )


@router.message(Command(BotCommand(command="web", description="登录 web 版")))
async def handle_web_login(message: Message, bot: Bot, language: str) -> None:
    await send_login_link(message.chat.id, bot, language)


# 点击inline button 的翻页按钮，刷新消息内容到下一页
@router.callback_query(ArchivedRoleSlideCallback.filter())
async def handle_role_slide(
    query: CallbackQuery,
    callback_data: ArchivedRoleSlideCallback,
    bot: Bot,
    state: FSMContext,
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    offset = callback_data.offset
    limit = callback_data.limit
    print(f"offset: {offset}, limit: {limit}")
    await bot_chat_history.send_archive_roles(
        query.from_user.id, query.message, state, offset, limit, True
    )


# 点击inline button， 返回指定角色的存档列表
@router.callback_query(ArchivedRoleSelectCallback.filter())
async def archive_role_select(
    query: CallbackQuery,
    callback_data: ArchivedRoleSelectCallback,
    bot: Bot,
    state: FSMContext,
):
    role_id = callback_data.role_id
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await bot_deeplink.handle_archive_click(
        query.from_user.id, role_id, query.message, bot, state
    )


@router.message(Command(BotCommand(command="new_roles", description="角色上新")))
async def handle_new_roles(
    message: types.Message, command: CommandObject, bot: Bot, state: FSMContext
):
    await chat_roles.send_role_card(message, bot, state, 1, tag=RoleTag.NEW.value)


@router.callback_query(NewRoleSlideCallback.filter())
async def handle_new_role_page(
    query: CallbackQuery,
    callback_data: NewRoleSlideCallback,
    bot: Bot,
    state: FSMContext,
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await chat_roles.send_role_card(
        query.message, bot, state, callback_data.page, True, callback_data.tag
    )


@router.callback_query(RoleListPageCallback.filter())
async def handle_page(
    query: CallbackQuery,
    callback_data: RoleListPageCallback,
    bot: Bot,
    state: FSMContext,
):
    if not query.message or not isinstance(query.message, types.Message):
        log.error(f"Invalid message in callback query: {query.message}")
        return
    await chat_roles.send_role_card(query.message, bot, state, callback_data.page)


@router.callback_query(RoleSelectCallback.filter())
async def handle_role_select(
    query: CallbackQuery, callback_data: RoleSelectCallback, bot: Bot, state: FSMContext
):
    role_id = callback_data.role_id
    user = await user_service.get_user_by_tg_id(query.from_user.id)
    if not user:
        await query.answer("请先注册，再选择角色")
        return
    await state.update_data({"role_id": role_id})
    await start_new_chat(query.message.chat.id, bot, state, user)


# async def _handle_archive_click(
#     tg_user_id: int, role_id: int, message: Message, bot: Bot, state: FSMContext
# ):
#     user = await user_service.get_user_by_tg_id(tg_user_id)
#     # 获取角色详情
#     role_map = await role_loader_service.map_user_brief_by_filter(
#         ids=[role_id],
#         language=Language.ZH.value,
#         nickname=user.nickname,
#         audit=False,
#         author=False,
#     )
#     state_data = await state.get_data()
#     now_conv_id = state_data.get("conversation_id", "") if state_data else ""
#     role = role_map.get(role_id)
#     if role is None:
#         await bot.send_message(message.chat.id, "角色不存在", parse_mode=ParseMode.HTML)
#         await state.set_state(None)
#         return

#     if role.image_nsfw:
#         avatar = str_util.handle_spoiler_avatar(role.role_avatar)
#     else:
#         avatar = str_util.format_avatar(role.role_avatar)
#     intro = str_util.format_tg_html_text(role.introduction)
#     content = f"""<b>{role.role_name}</b>\n{intro}"""
#     await bot.send_photo(message.chat.id, photo=avatar, caption=content)

#     # 通过role_id和user获取用户的存储记录信息
#     archive_list = await chat_message_service.list_conversation(
#         user.id, ChatModeType.SINGLE.value, role_id
#     )
#     archive_list = archive_list[:3] if len(archive_list) > 3 else archive_list

#     bot_info = await bot.get_me()
#     bot_user_name = bot_info.username
#     html_text = "<b>选择存档</b>\n"
#     for archive in archive_list:
#         conversation_id = archive.conversation_id
#         line = f"{archive.title}{"（当前存档）" if now_conv_id == archive.conversation_id else ""}"
#         html_text += f'<a href="https://t.me/{bot_user_name}?start=role_archive_{role_id}_{conversation_id}">{line}</a>\n首次聊天: {archive.latest_chat_at}\n最后聊天: {archive.latest_chat_at}\n\n'

#     await bot.send_message(message.chat.id, html_text, parse_mode=ParseMode.HTML)
#     await state.set_state(None)


# # 处理存档command的deeplink
# async def _handle_archive_deeplink(
#     args: str, message: Message, bot: Bot, state: FSMContext
# ):
#     if args.startswith("archive_"):
#         # 返回指定角色的存档列表
#         role_id = int(args.replace("archive_", ""))
#         await _handle_archive_click(message.from_user.id, role_id, message, bot, state)
#     elif args.startswith("role_archive_"):
#         role_id, conversation_id = args.replace("role_archive_", "").split("_")
#         await _handle_role_archive_link(
#             message.from_user.id, int(role_id), conversation_id, message, bot, state
#         )
#     else:
#         return


# # 获取指定存档的历史聊天记录，并发消息
# async def _handle_role_archive_link(
#     tg_user_id: int,
#     role_id: int,
#     conversation_id: str,
#     message: Message,
#     bot: Bot,
#     state: FSMContext,
# ):
#     role = await role_loader_service.load_by_id(role_id)
#     user = await user_service.get_user_by_tg_id(tg_user_id)
#     if not user or not role:
#         return
#     history_query = BuildHistoryQuery(
#         user_id=user.id,
#         nickname=user.nickname,
#         mode_type=ChatModeType.SINGLE.value,
#         mode_target_id=role_id,
#         conversation_id=conversation_id,
#         language=Language.ZH.value,
#         add_name=False,
#         # regex_option=RegexOption.FORMAT_DISPLAY.value,
#     )
#     chat_list = await chat_message_service.build_history(history_query)
#     # 取最新的3轮聊天
#     target_chat_list = chat_list[-6:]
#     chat_text_list = _handle_msg_history(
#         target_chat_list, False, user.status_block_switch, role.role_name, user.nickname
#     )
#     await state.update_data({"conversation_id": conversation_id, "role_id": role_id})
#     try:
#         await bot.send_message(
#             message.chat.id, chat_text_list[0], parse_mode=ParseMode.HTML
#         )
#         # 检查当前用户选择的聊天模型是否支持该角色(不支持，就发对应消息)
#         await role_access_service.handle_bot_chat_role_support_model_check(
#             message.chat.id, bot, user, role, conversation_id, chat_list[-1].message_id
#         )
#     except Exception as e:
#         logging.error(
#             f"send message error,user_id:{user.id},chat_text:{chat_text_list[0]}"
#         )
#         raise e


# def _handle_msg_history(
#     target_chat_list: list[ChatHistoryItem],
#     need_split: bool = False,
#     status_block_switch: bool = True,
#     role_name: str = "",
#     user_name: str = "",
# ) -> list[str]:
#     for history in target_chat_list:
#         # content = history.content
#         # # remove all <sp> content
#         # content = re.sub(r'<!--.*?-->','', content, flags=re.DOTALL)
#         # content = re.sub(r'<wit>.*?</wit>', '', content, flags=re.DOTALL)
#         # content = re.sub(r"<StatusBlock>.*</StatusBlock>", "", content, flags=re.DOTALL)
#         # content = re.sub(r'<sp>.*?</sp>', '', content, flags=re.DOTALL)
#         # # remove all xml tags in fm
#         # content = re.sub(r'<[^>]+>', '', content)

#         content = message_utils.bot_message_display_format(
#             role_name, history.content, status_block_switch
#         )
#         prefix = role_name if history.type == ChatHistoryType.AI.value else user_name
#         history.content = f"<b>{prefix}: </b>{content.strip()}"
#     if need_split:
#         # target_chat_list 每2个元素1组来拆分
#         target_chat_list_split = [
#             target_chat_list[i : i + 2] for i in range(0, len(target_chat_list), 2)
#         ]
#         chat_text_list = []
#         for split_chat_list in target_chat_list_split:
#             chat_text = "\n\n\n".join([chat.content for chat in split_chat_list])
#             chat_text_list.append(chat_text)
#         return chat_text_list
#     else:
#         chat_text = "\n\n\n".join([chat.content for chat in target_chat_list])
#         if len(chat_text) > 4000:
#             chat_text = "..." + chat_text[-4000:]
#         return [chat_text]


# # 处理角色卡分享的deeplink
# async def _handle_role_share_deeplink(
#     args: str, message: Message, bot: Bot, state: FSMContext
# ):
#     if not args.startswith("roleshare_"):
#         return
#     share_id = int(args.replace("roleshare_", ""))
#     share_detail = await user_share_service.get_role_share_by_id(share_id)
#     role = await role_config_service.RoleConfigService.get_role_config(
#         share_detail.role_id
#     )
#     if not role or not share_detail:
#         logging.error(f"角色卡分享的deeplink参数错误: {args}")
#         return
#     if role.image_nsfw:
#         avatar = str_util.handle_spoiler_avatar(role.role_avatar)
#     else:
#         avatar = str_util.format_avatar(role.role_avatar)

#     sharer_user = await user_service.safe_get_user(share_detail.user_id)
#     nickname = sharer_user.nickname if sharer_user else ""
#     history_query = BuildHistoryQuery(
#         user_id=share_detail.user_id,
#         nickname=nickname,
#         mode_type=ChatModeType.SINGLE.value,
#         mode_target_id=role.id,
#         conversation_id=share_detail.conversation_id,
#         language=Language.ZH.value,
#         add_name=False,
#         # regex_option=RegexOption.FORMAT_DISPLAY.value,
#     )
#     chat_list = await chat_message_service.build_history(
#         history_query, share_detail.content_snapshot
#     )
#     chat_msg_list = _handle_msg_history(chat_list, True, True, role.role_name, nickname)

#     content = f"<b>{share_detail.title}</b>\n\n"
#     if share_detail.description:
#         content += f"{share_detail.description}\n\n"
#     content += "<b>选择存档</b>\n"

#     bot_info = await bot.get_me()
#     bot_user_name = bot_info.username
#     # {'role_id': 123, 'inviter': 456}
#     payload = json.dumps({"role_id": share_detail.role_id})
#     encoded_str = encode_payload(payload)
#     html_text = f'1.<a href="https://t.me/{bot_user_name}?start={encoded_str}">重新开始</a>\n2.分享者存档，接着聊（功能开发中）'
#     content += html_text
#     await bot.send_photo(
#         message.chat.id, photo=avatar, caption=content, parse_mode=ParseMode.HTML
#     )
#     for chat_msg in chat_msg_list:
#         await bot.send_message(message.chat.id, chat_msg, parse_mode=ParseMode.HTML)


# # 处理「聊天返钻石活动」的报名deeplink
# async def _handle__diamond_season_enroll_deeplink(
#     args: str, message: Message, bot: Bot, state: FSMContext
# ):
#     if not args.startswith("act_enroll_"):
#         return
#     task_id = args.replace("act_enroll_", "")
#     if task_id == "":
#         return
#     user = await user_service.get_user_by_tg_id(message.from_user.id)
#     if user is None:
#         return
#     role_ids, msg = await user_diamond_season_service.enroll_diamond_season_activity(
#         user.id, task_id
#     )
#     await bot.send_message(message.chat.id, msg, parse_mode=ParseMode.HTML)
#     if role_ids:
#         # -1 表示支持所有角色卡
#         if -1 not in role_ids:
#             role_ids_str = ",".join([str(role_id) for role_id in role_ids])
#             await send_activity_role_card(
#                 message, bot, state, 0, role_ids_str, False, task_id
#             )
#         else:
#             await get_role_list(message, bot, state)
#             await state.set_state(None)


# 处理自动帮用户切换模型的inline button
@router.callback_query(ModelAutoChangeCallback.filter())
async def model_change(
    query: CallbackQuery,
    callback_data: ModelAutoChangeCallback,
    bot: Bot,
    state: FSMContext,
):
    mid = callback_data.mid
    if not mid:
        # 重新选择角色
        await handle_list(query.message, None, bot, state)
    else:
        user = await user_service.get_user_by_tg_id(query.from_user.id)
        if not user:
            return
        message_template = await bot_model_switch.model_select_template(user, mid)
        await bot.send_message(
            chat_id=query.message.chat.id,
            text=message_template.tips,
            parse_mode=ParseMode.HTML,
        )


# 处理「聊天返钻石活动」的 领取inline button
@router.callback_query(DiamondSeasonRewardCallback.filter())
async def handle_diamond_season_reward(
    query: CallbackQuery,
    bot: Bot,
    callback_data: DiamondSeasonRewardCallback,
    state: FSMContext,
):
    task_id = callback_data.task_id
    user = await user_service.get_user_by_tg_id(query.from_user.id)

    if user is None or user.id != callback_data.user_id:
        await bot.send_message(
            query.message.chat.id, "领取链接不合法", parse_mode=ParseMode.HTML
        )
        return

    _, msg = await user_diamond_season_service.receive_diamond_reward(user.id, task_id)
    await bot.send_message(query.message.chat.id, msg, parse_mode=ParseMode.HTML)


@router.callback_query(operation_service.ComCallbackCommand.filter())
async def handle_com_callback_command(
    query: CallbackQuery, callback_data: operation_service.ComCallbackCommand, bot: Bot
):
    tg_id = query.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    if not user:
        await query.answer("请先注册")
        return
    if callback_data.command == "free_benefit":
        await bot_cmd.free_benefit(user, tg_id, bot)
    if callback_data.command == "settings":
        await bot_model_switch.send_settings(
            user, tg_id, query.message.message_id, bot, None
        )


@router.message(Command(BotCommand(command="role_bot", description="角色橱窗")))
async def command_role_bot(message: Message, command: CommandObject) -> None:
    button = Button(text="打开橱窗", url=user_growth_constants.ROLE_CHANNEL_LINK)
    template = MessageTemplate(
        tips="打开角色橱窗，一起浏览和讨论所有角色", buttons=[button]
    )
    await message.answer(template.tips, reply_markup=template.as_markup())


@role_bot_router.post("/role_hook/{bot_id}")
async def webhook_handler(
    bot_id: str, update: dict, x_telegram_bot_api_secret_token: str = Header(...)
):
    if x_telegram_bot_api_secret_token != WEBHOOK_SECRET:
        return "Invalid token"
    logging.info(f"role_bot webhook_handler: {bot_id}, {update}")
    try:
        bot = bots.get(bot_id.upper())
        if bot is None:
            bot = chat_bot
        language = Language.ZH.value
        bot_config = await tg_config_service.get_bot_config_by_id(bot.id)
        if bot_config and bot_config.lang:
            language = bot_config.lang
        if not bot_config:
            log.warning(f"Bot config not found for bot_id: {bot_id}")
        await dp.feed_raw_update(bot=bot, update=update, language=language)
        message = update.get("message")
        if message and "from" in message and "id" in message.get("from"):
            tg_id = message.get("from").get("id")
            is_bot = message.get("from").get("is_bot")
            if is_bot:
                return
            await user_active_service.refresh_by_tg(bot.id, tg_id)
            await send_transfer_message(tg_id, bot)
    except Exception as e:
        logging.exception(e)
