import asyncio
from typing import As<PERSON><PERSON><PERSON><PERSON>, Iterator
from langchain_core.messages import BaseMessageChunk
from datetime import datetime
from itertools import groupby
import logging
import re
import os
import uuid
from aiogram import Bot, types
from aiogram.enums import ParseMode
from aiogram.filters import CommandObject
from aiogram.types import Message
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.fsm.context import FSMContext
from ai import lite_llm_bot, new_chat_bot, voice
from common import bot_common
from common.models.chat_model import ChatBotStateData, ChatHistoryType, ChatNextInput
from common.models.chat_request import ChatRequest
from controllers.bot_hooks.bot_services import bot_operation
from controllers.bot_hooks.bot_tasks import try_send_tasks

from persistence.redis_client import async_func_lock
from services.account_service import AccountService
from services.chat import chat_message_service, chat_model_switch_history
from persistence.models.models import RegexOption, TgBotConfig, User, UserStatus
from persistence.presets import <PERSON>SFWLevel, Scenario, presetsPersistence
from common.common_constant import (
    ChatApiVersion,
    ChatChannel,
    ChatPlatform,
    <PERSON>rror<PERSON>ey,
    Language,
    ModelEventType,
    PopupShowPeriod,
    PopupType,
)
from services import (
    operation_service,
    product_service,
    regex_service,
    role_access_service,
    tg_config_service,
    tg_message_service,
    user_diamond_season_service,
    user_growth_service,
    user_service,
)
from services import message_service
from aioitertools import itertools as aioitertools
from services.user import user_chat_service
from utils.translate_util import _t, _tl
from .bot_setting import (
    ActivityDiamondSeasonEnrollCallback,
    RegenerateCallback,
    VoiceCallback,
    router,
    safe_clear_markup,
    safe_clear_message,
    start_new_chat,
)
from controllers.bot_hooks import bot_setting

log = logging.getLogger(__name__)

background_tasks = set()


async def atomic_do_chat(
    bot: Bot,
    chat_id: int,
    user: User,
    user_input: str,
    state: FSMContext,
    is_retry: bool = False,
    retry_message_id: str = "",
):
    tg_config: TgBotConfig = await tg_config_service.get_bot_config_by_id(bot.id)  # type: ignore
    language = tg_config.lang

    async def lock_fail():
        error_message = await bot.send_message(
            chat_id,
            f"{user_input}{_tl("（发送失败）\n（请等待上一条消息完成哦~）", language)}",
        )
        log.warning(f"repeat message, tg_uid: {user.id},uid: {user.id}")

        async def del_msg():
            await asyncio.sleep(5)
            await error_message.delete()

        asyncio.create_task(del_msg())
        return

    @async_func_lock("BotChat", str(user.id), 30, lock_fail)
    async def run_task():
        await do_chat_new(
            bot, chat_id, user, user_input, state, is_retry, retry_message_id, language
        )

    asyncio.create_task(run_task())


async def do_chat_new(
    bot: Bot,
    chat_id: int,
    user: User,
    user_input: str,
    state: FSMContext,
    is_retry: bool = False,
    retry_message_id: str = "",
    language: str = Language.ZH.value,
):
    state_data = await state.get_data()
    bot_state_data = ChatBotStateData(**state_data) if state_data else None
    if (
        not bot_state_data
        or not bot_state_data.role_id
        or not bot_state_data.conversation_id
    ):
        return
    payload = ChatRequest(
        role_id=bot_state_data.role_id,
        message=user_input,
        conversation_id=bot_state_data.conversation_id,
        isRetry=is_retry,
        retry_message_id=retry_message_id,
        language=language,
        api_version=ChatApiVersion.V2.value,
        platform=ChatPlatform.CHAT_BOT.value,
    )
    log.info(
        f"do_chat_new user: {user.id}, payload: {payload}, state_data: {state_data}"
    )
    input = await user_chat_service.build_chat_input_param(
        user.id, payload, language
    )
    # channel_switch = await auto_switch_chat_channel(user, input, bot, chat_id)
    # if channel_switch:
    #     return
    verify = await role_access_service.verify_bot_chat_auth(
        user, bot, chat_id, payload, input
    )
    if not verify:
        return

    if not is_retry and len(user_input) > 0:
        await chat_message_service.save_user_message(
            user, input, payload, ChatPlatform.CHAT_BOT.value
        )
    if is_retry and bot_state_data.last_mids:
        await safe_clear_message(bot, chat_id, bot_state_data.last_mids)
        bot_state_data.last_mids = []
        await state.update_data({"last_mids": None})
    if bot_state_data.last_mids:
        await safe_clear_markup(bot, chat_id, bot_state_data.last_mids)
        await state.update_data({"last_mids": None})
    input = await chat_message_service.build_model_config(input, Scenario.CHAT.value)
    input = await chat_message_service.build_request_history(input)
    input = await chat_message_service.build_character_book(input)
    input = await chat_message_service.build_user_custom_persona(input)
    builder = InlineKeyboardBuilder()
    builder.button(
        text=_tl("语音", language),
        callback_data=VoiceCallback(message_id=input.message_id, version=input.version),
    )
    builder.button(
        text=_tl("重新生成", language),
        callback_data=RegenerateCallback(
            message_id=input.message_id, version=input.version
        ),
    )
    send_message = await bot.send_message(chat_id, _tl("思考中✏️……", language))

    async def async_role_next(input: ChatNextInput, retry: bool = True):
        try:
            chat_response = await chat_message_service.llm_call_auth_retry(input)
            if not chat_response.success or not chat_response.response:
                ret_msg = _tl(ErrorKey.CHAT_CONTENT_ERROR.value, language)
                await send_message.edit_text(ret_msg)
                return
            ret = await message_service.bot_chat_result_iter_new(
                user,
                payload,
                input,
                chat_response.first_chunk,
                chat_response.response,
                send_message,
                builder.as_markup(),
                chat_response.token_sum,
            )

            if ret and ret.success:
                data = {"last_mids": [send_message.message_id]}
                await state.update_data(data)
                return
            if retry and not input.auto_retry:
                await async_role_next(input, False)
                return
        except Exception as e:
            log.error(f"process_message_content_v1 error,user_id:{user.id}: {e}")
            await send_message.edit_text(_tl("系统异常，请稍后重试", language))
            return

    await async_role_next(input)

    bot_config = await tg_config_service.get_bot_config_by_id(bot.id)  # type: ignore
    if bot_config and bot_config.en_bot():
        if len(input.history) == 2:
            btn_builder = InlineKeyboardBuilder()
            btn_builder.button(
                text=_tl("Upgrade to Mini App", language),
                url=f'{bot_config.url}/tavern'
            )
            await bot.send_message(chat_id, _tl("Upgrade to Mini App enjoy the full experience.", language), reply_markup=btn_builder.as_markup())
    else:
        await try_send_tasks(bot, chat_id, user, bot_state_data.role_id)

    # if len(input.history) >= 4:
    # await operation_service.check_bot_pay_switch_message(user, chat_id, bot)
    await _send_diamond_activity_related_msg(bot, chat_id, user, bot_state_data.role_id)
    await bot_operation.chat_notice(bot, user, chat_id, state, input)
    await bot_operation.auto_switch_chat_channel(user, input, bot, chat_id, state)


# async def do_chat(
#     bot: Bot,
#     chat_id: int,
#     user: User,
#     user_input: str | None,
#     state: FSMContext,
#     is_retry: bool = False,
#     retry_message_id: str = "",
# ):
#     state_data = await state.get_data()
#     log.info(
#         f"do_chat user_id: {user.id}, user_input: {user_input}, state_data: {state_data},is_retry: {is_retry}, retry_message_id: {retry_message_id}"
#     )
#     if user.status == UserStatus.CHAT_BLACK.value:
#         error = _t(ErrorKey.USER_IN_BLACKLIST.value, Language.ZH.value)
#         await bot.send_message(chat_id, error)
#         return
#     last_mids = state_data.get("last_mids")
#     if last_mids and len(last_mids) > 0:
#         await safe_clear_markup(bot, chat_id, last_mids)
#         await state.update_data({"last_mids": None})
#     conversation_id = state_data.get("conversation_id", "")
#     role_id = state_data.get("role_id", 0)
#     if not user or role_id == 0:
#         log.warning(f"User or role not found: {user.id}, {role_id}")
#         return
#     if user_input == None:
#         log.warning(f"User input is None: {user.id}, {state_data}")
#         return
#     payload = ChatRequest(
#         role_id=role_id,
#         message=user_input,
#         conversation_id=conversation_id,
#         isRetry=is_retry,
#         retry_message_id=retry_message_id,
#         language=Language.ZH.value,
#     )
#     input = await chat_message_service.build_chat_input_param(
#         user.id, payload, Language.ZH.value
#     )
#     verify = await role_access_service.verify_bot_chat_auth(
#         user, bot, chat_id, payload, input
#     )
#     if not verify:
#         return

#     if not is_retry and len(user_input) > 0:
#         await chat_message_service.save_user_message(
#             user, input, payload, ChatPlatform.CHAT_BOT.value
#         )
#     if is_retry and state_data.get("last_mids"):
#         await safe_clear_message(bot, chat_id, state_data.get("last_mids", []))
#         await state.update_data({"last_mids": None})
#     input = await chat_message_service.build_model_config(input, Scenario.CHAT.value)
#     input = await chat_message_service.build_request_history(input)
#     input = await chat_message_service.build_character_book(input)
#     input = await chat_message_service.build_user_custom_persona(input)

#     chat_response = await lite_llm_bot.role_next(input)
#     history_iter, response_iter = aioitertools.tee(
#         chat_response.response.fetch_sync_stream()
#     )

#     save_task = asyncio.create_task(
#         message_service.save_new_history_v1(
#             user, payload, input, history_iter, chat_response.token_sum, "CHAT_BOT"
#         )
#     )
#     background_tasks.add(save_task)
#     save_task.add_done_callback(background_tasks.discard)

#     builder = InlineKeyboardBuilder()
#     builder.button(
#         text="语音",
#         callback_data=VoiceCallback(message_id=input.message_id, version=input.version),
#     )
#     builder.button(
#         text="重新生成",
#         callback_data=RegenerateCallback(
#             message_id=input.message_id, version=input.version
#         ),
#     )
#     send_message = await bot.send_message(chat_id, "思考中✏️……")
#     mids = [send_message.message_id]

#     async def edit_message(content: str, end: bool = False):
#         if end:
#             await send_message.edit_text(content, reply_markup=builder.as_markup())
#             return
#         await send_message.edit_text(content)

#     chat_finish_data = None

#     try:
#         chat_finish_data = await message_service.bot_chat_result_iter_v1(
#             input, response_iter, edit_message
#         )
#     except Exception as e:
#         log.error(f"process_message_content_v1 error: {e}")
#         await send_message.edit_text("系统异常，请重试")

#     data = {"last_mids": mids}
#     await state.update_data(data)
#     await try_send_tasks(bot, chat_id, user, role_id)

#     if len(input.history) >= 4:
#         await operation_service.check_bot_pay_switch_message(user, chat_id, bot)

#     await _send_diamond_activity_related_msg(bot, chat_id, user, role_id)

#     return chat_finish_data


# 钻石消耗活动相关的消息
async def _send_diamond_activity_related_msg(
    bot: Bot, chat_id: int, user: User, role_id: int
):
    # en bot 不发送活动消息
    bot_config = await tg_config_service.get_bot_config_by_id(bot.id)  # type: ignore
    if bot_config and bot_config.en_bot():
        return

    user_id = user.id
    task, is_enrolled = (
        await user_diamond_season_service.get_enrolled_active_diamond_task_by_user_id(
            user_id
        )
    )
    if task is None:
        return
    task_id = str(task.task_id)
    if not is_enrolled:
        # 判断是否要提醒报名
        payed_balance = await AccountService.get_payed_total_balance(user_id)
        if payed_balance <= 0 or payed_balance >= task.required_diamond_amount:
            return
        result, _ = await user_diamond_season_service.check_max_participants(task)
        if not result:
            return
        result, _ = await user_diamond_season_service.check_exclusive_enrollment(
            task, user_id
        )
        if not result:
            return
        # 聊天过程中提醒报名的消息，只发一次
        identifier = f"{task_id}{PopupShowPeriod.ONCE.value}"
        pop_up_recored = await operation_service.get_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_NOTICE_BOT
        )
        if pop_up_recored:
            return
        # 发送报名的消息
        msg = await user_diamond_season_service.generate_notify_enroll_msg(task)
        builder = InlineKeyboardBuilder()
        builder.button(
            text="报名",
            callback_data=ActivityDiamondSeasonEnrollCallback(task_id=task_id),
        )
        await bot.send_message(
            chat_id, msg, parse_mode=ParseMode.HTML, reply_markup=builder.as_markup()
        )
        await operation_service.add_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_NOTICE_BOT
        )
    else:
        # 判断是否要提醒充值
        msg = await user_diamond_season_service.generate_recharege_msg_for_bot(
            task, user_id
        )
        if msg is None:
            return
        payed_balance = await AccountService.get_payed_total_balance(user_id)
        if payed_balance > 0:
            return
        # 聊天过程中提醒充值的消息，只发一次
        identifier = f"{task_id}{PopupShowPeriod.ONCE.value}"
        pop_up_recored = await operation_service.get_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_RECHARGE_BOT
        )
        if pop_up_recored:
            return
        button_builder = await bot_common.create_charge_button_builder(
            bot, user.id, False
        )
        await operation_service.add_popup_record(
            user_id, identifier, PopupType.DIAMOND_ACTIVITY_RECHARGE_BOT
        )
        await bot.send_message(
            chat_id=chat_id,
            text=msg,
            parse_mode=ParseMode.HTML,
            reply_markup=button_builder.as_markup(),
        )


# async def process_message_content_v1(
#     bot: Bot,
#     chat_id: int,
#     input: ChatNextInput,
#     response_iter: AsyncIterator[BaseMessageChunk],
# ):

#     builder = InlineKeyboardBuilder()
#     builder.button(
#         text="语音",
#         callback_data=VoiceCallback(message_id=input.message_id, version=input.version),
#     )
#     builder.button(
#         text="重新生成",
#         callback_data=RegenerateCallback(
#             message_id=input.message_id, version=input.version
#         ),
#     )
#     send_message = await bot.send_message(chat_id, "思考中✏️……")

#     async def edit_message(content: str, end: bool = False):
#         if end:
#             await send_message.edit_text(content, reply_markup=builder.as_markup())
#             return
#         await send_message.edit_text(content)

#     try:
#         chat_finish_data = await message_service.bot_chat_result_iter(
#             input, response_iter, edit_message
#         )
#         return [send_message.message_id], chat_finish_data
#     except Exception as e:
#         log.error(f"process_message_content_v1 error: {e}")
#         await send_message.edit_text("系统异常，请重试")

#     return [send_message.message_id], None


@router.callback_query(VoiceCallback.filter())
async def voice_callback(
    callback: types.CallbackQuery,
    callback_data: VoiceCallback,
    bot: Bot,
    state: FSMContext,
):
    tg_id = callback.from_user.id
    user = await user_service.get_user_by_tg_id(tg_id)
    message_id = callback_data.message_id
    version = callback_data.version
    if not user or not message_id or not version:
        log.error(f"voice_callback param error: {tg_id}, {message_id}, {version}")
        return

    voice_url = await voice.generate_voice_new(user, message_id, callback_data.version)
    if not voice_url:
        log.error(f"voice_callback empty: {user.id}, {message_id}, {version}")
        return
    await callback.message.answer_audio(voice_url)
    # state_data = await state.get_data()
    # last_message_id = state_data.get('last_op_message_id')
    # if last_message_id is not None:
    #     await safe_clear_message(bot, callback.message.chat.id, [last_message_id])
    #     await state.update_data({'last_op_message_id': None})


@router.callback_query(RegenerateCallback.filter())
async def regenerate_callback(
    callback: types.CallbackQuery,
    callback_data: RegenerateCallback,
    bot: Bot,
    state: FSMContext,
):
    tg_user_id = callback.from_user.id
    user = await user_service.get_user_by_tg_id(tg_user_id)
    if not user:
        log.error(f"regenerate_callback user not found: {tg_user_id}")
        return
    log.info(
        f"regenerate_callback user: {user.id}, message_id: {callback_data.message_id}"
    )
    await atomic_do_chat(
        bot,
        callback.message.chat.id,
        user,
        "",
        state,
        True,
        callback_data.message_id,
    )
    # chat_finish_data = await do_chat(
    #     bot,
    #     callback.message.chat.id,
    #     user,
    #     "",
    #     state,
    #     True,
    #     callback_data.message_id,
    # )
    # # 自动重试
    # if chat_finish_data and chat_finish_data.need_retry:
    #     await do_chat(
    #         bot,
    #         callback.message.chat.id,
    #         user,
    #         "",
    #         state,
    #         True,
    #         callback_data.message_id,
    #     )


async def reset_chat(
    message: Message, command: CommandObject, bot: Bot, state: FSMContext
) -> None:
    user = await user_service.get_user_by_tg_id(message.from_user.id)
    await start_new_chat(message.chat.id, bot, state, user)
