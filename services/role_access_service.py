import logging
from urllib.parse import quote
from aiogram import <PERSON><PERSON>
from aiogram.enums import ParseMode
from fastapi import Response
from fastapi.responses import JSONResponse
from common import bot_common
from common.common_constant import (
    ChatChannel,
    ChatModeType,
    ErrorCode,
    ErrorKey,
    Language,
    ModelEventType,
    ProductPermission,
    ProductType,
    ChatApiVersion,
)
from common.models.chat_model import ChatNextInput, HistoryRequest
from common.models.chat_request import ChatRequest, ImpersonateRequest, ModelSwitch
from common.role_model import ProductResponse
from controllers import user_check
from persistence.models.models import Product, RoleConfig, User, UserStatus
from services import bot_config_service, bot_message_service, product_service, tg_config_service
from services.chat import chat_message_service, chat_model_switch_history
from services.user_service import user_service
from services.role import role_group_service, role_loader_service
from utils import json_util, message_utils, response_util
from utils.translate_util import _t, _tl
from aiogram.utils.keyboard import InlineKeyboardBuilder

log = logging.getLogger(__name__)


def allow_chat(user: User, role: RoleConfig | None) -> bool:
    if not role:
        return False
    if role.privacy:
        return True
    if role.uid == user.id:
        return True
    return False


def allow_new_chat(chat_input: ChatNextInput) -> bool:
    if chat_input.privacy or chat_input.role_created_uid == chat_input.user_id:
        return True
    return False


def manage_user(user: User):
    if user and user.status == UserStatus.ADMIN.value:
        return True
    return False


async def verify_history_request(history_req: HistoryRequest, user: User):
    if manage_user(user):
        return None
    if history_req.role_id == 0:
        return None
    role = await role_loader_service.get_by_id(history_req.role_id)
    if not allow_chat(user, role):
        return response_util.json_param_error("role not allow")
    return None


# FREE_MODELS = [
#     LlmModel.CLAUDE_3_HAIKU.value,
#     LlmModel.DEEPSEEK_V3_VOLCENGINE.value,
#     LlmModel.QWEN_TURBO.value,
# ]


def check_first_free_model_by_model_list(product_list: list[Product]) -> Product | None:
    for product in product_list:
        if product.permission == ProductPermission.ALL_USER.value:
            return product
    return None


async def check_need_model_switch(support_mids: list[str], user: User):
    user_chat_product = await product_service.get_user_chat_product(user)
    if user_chat_product and user_chat_product.mid in support_mids:
        return None
    mid_product_map = await product_service.map_mid_chat_product()
    mid_price_map = {x.mid: x.price for x in mid_product_map.values()}
    ret_support_mids = [x for x in support_mids if x in mid_price_map]
    ret_support_mids.sort(key=lambda x: mid_price_map.get(x, 100000))
    payed_user = await user_service.is_payed_user(user.id)

    def check_recharge_first(product: Product):
        return (
            not payed_user and product.permission == ProductPermission.PAID_USER.value
        )

    for mid in ret_support_mids:
        product = mid_product_map.get(mid)
        if product and product.permission == ProductPermission.ALL_USER.value:
            return ModelSwitch(target_model=mid, need_recharge_first=False)
    ret_product = mid_product_map.get(ret_support_mids[0])
    return ModelSwitch(
        target_model=ret_support_mids[0],
        need_recharge_first=check_recharge_first(ret_product),  # type: ignore
    )


async def check_model_switch_needed(role: RoleConfig, user: User):
    target_switch_product, is_free_model, unsupported_products, supported_products = (
        await check_model_switch_needed_helper(role, user)
    )
    if not target_switch_product:
        return None, None, None

    is_payed_user = await user_service.is_payed_user(user.id)
    is_amdin = manage_user(user)
    ignore_paid_model_restriction = is_payed_user or is_amdin
    model_switch = ModelSwitch(
        target_model=target_switch_product.mid,
        need_recharge_first=not (is_free_model or ignore_paid_model_restriction),
    )
    return model_switch, unsupported_products, supported_products


async def check_model_switch_needed_helper(role: RoleConfig, user: User):
    excluded_product_ids = json_util.convert_to_list(role.excluded_product_ids)
    user_product = await product_service.get_user_chat_product(user)
    if not user_product:
        return None, None, None, None
    user_model_id = user_product.mid
    if user_model_id not in excluded_product_ids:
        # 用户当前选择的模型在该角色支持的模型列表中，直接返回
        return None, None, None, None
    log.info(
        f"Role does not support user default product model, user_id: {user.id}, role_id: {role.id}, user_model: {user_model_id}, excluded_product_ids: {excluded_product_ids}"
    )
    return await get_cheapest_and_unsupported_and_supported_products_by_role(role)


# 获取当前模型支持的最实惠的模型（优先从免费模型中返回消耗最少的；没有免费模型就返回消耗最少的付费模型）和不支持的online模型列表
async def get_cheapest_and_unsupported_and_supported_products_by_role(role: RoleConfig):
    list_original_products = await Product.filter(
        online=True, type=ProductType.CHAT.value
    ).all()
    list_original_products.sort(key=lambda x: x.price)
    excluded_product_ids = json_util.convert_to_list(role.excluded_product_ids)
    supported_original_products = [
        p for p in list_original_products if p.mid not in excluded_product_ids
    ]
    unsupported_original_products = [
        p for p in list_original_products if p.mid in excluded_product_ids
    ]
    unsupported_products = [
        ProductResponse.from_product(p) for p in unsupported_original_products
    ]
    free_original_product = check_first_free_model_by_model_list(
        supported_original_products
    )
    supported_products = [
        ProductResponse.from_product(p) for p in supported_original_products
    ]
    if free_original_product:
        # True 表示是免费模型
        return (
            ProductResponse.from_product(free_original_product),
            True,
            unsupported_products,
            supported_products,
        )
    else:
        # False 表示是付费模型
        return (
            ProductResponse.from_product(supported_original_products[0]),
            False,
            unsupported_products,
            supported_products,
        )


async def get_support_product_list_by_role(role: RoleConfig):
    products = await product_service.list_chat_product_new()
    product_ids = [x.mid for x in products]
    excluded_product_ids = json_util.convert_to_list(role.excluded_product_ids)
    if excluded_product_ids:
        support_product_ids = [x for x in product_ids if x not in excluded_product_ids]
    else:
        support_product_ids = product_ids
    return support_product_ids


async def get_support_product_list_by_role_ids(role_ids: list[int]):
    roles = await role_loader_service.list_by_ids(role_ids)
    products = await product_service.list_chat_product_new()
    support_product_ids = [x.mid for x in products]
    for role in roles:
        excluded_product_ids = json_util.convert_to_list(role.excluded_product_ids)
        if not excluded_product_ids:
            continue
        support_product_ids = [
            x for x in support_product_ids if x not in excluded_product_ids
        ]
    return support_product_ids


# async def allow_model(model: str, user: User) -> bool:
#     if manage_user(user):
#         return True
#     if not model or model in FREE_MODELS:
#         return True
#     is_payed_user = await user_service.is_payed_user(user.id)
#     if is_payed_user:
#         return True
#     return False


async def allow_model_by_product(product: Product, user: User) -> bool:
    if manage_user(user):
        return True
    if not product or product.permission == ProductPermission.ALL_USER.value:
        return True
    is_payed_user = await user_service.is_payed_user(user.id)
    if is_payed_user:
        return True
    return False


async def verify_chat_str(user: User, payload: ChatRequest, input: ChatNextInput):
    if not payload.verify_param():
        log.error(f"Invalid chat request, user_id: {user.id}")
        return ErrorKey.PARAM_ERROR.value
    chat_product = await product_service.get_user_chat_product(user)
    if not chat_product:
        return ErrorKey.MODEL_OFFLINE_ERROR.value

    if input.balance < chat_product.price and input.chat_free_benefit <= 0:
        log.warning(f"Insufficient AllBalance, user_id: {user.id}")
        return ErrorKey.INSUFFICIENT_BALANCE.value
    if (
        input.chat_channel == ChatChannel.FREE_BENEFIT.value
        and input.chat_free_benefit <= 0
    ):
        log.warning(
            f"Insufficient FreeBenefit, user_id: {user.id},chat_product: {chat_product.mid}"
        )
        return ErrorKey.INSUFFICIENT_FREE_BENEFIT.value
    if (
        input.chat_channel == ChatChannel.PAID.value
        and input.balance < chat_product.price
    ):
        log.warning(
            f"Insufficient Balance, user_id: {user.id},chat_product: {chat_product.mid}"
        )
        return ErrorKey.INSUFFICIENT_BALANCE.value

    if not await allow_model_by_product(chat_product, user):
        log.error(
            f"Model not allowed, user_id: {user.id},chat_product: {chat_product.mid}"
        )
        return ErrorKey.MODEL_ONLY_FOR_PAID_USER.value

    if user.status == UserStatus.CHAT_BLACK.value:
        log.error(f"User is in chat black list, user_id: {user.id}")
        return ErrorKey.USER_IN_BLACKLIST.value

    is_admin = manage_user(user)

    role_config = None
    group_config = None
    if payload.mode_type == ChatModeType.SINGLE.value:
        role_config = await role_loader_service.get_by_id(payload.role_id)
    if payload.mode_type == ChatModeType.GROUP.value:
        group_config = await role_group_service.get_original(payload.group_id)
    if role_config and not role_config.status and not is_admin:
        log.warning(f"Role not allowed, user_id: {user.id}, role_id: {payload.role_id}")
        return ErrorKey.ROLE_DELETED.value
    if group_config and not group_config.status and not is_admin:
        log.warning(
            f"Group not allowed, user_id: {user.id}, group_id: {payload.group_id}"
        )
        return ErrorKey.ROLE_DELETED.value
    if (
        role_config
        and (not role_config.privacy and role_config.uid != user.id)
        and not is_admin
    ):
        log.error(f"Role not allowed, user_id: {user.id}, role_id: {payload.role_id}")
        return ErrorKey.INSUFFICIENT_ROLE_LEVEL.value

    if is_admin:
        return None

    if group_config and not group_config.status:
        log.error(
            f"Group not allowed, user_id: {user.id}, group_id: {payload.group_id}"
        )
        return ErrorKey.PARAM_ERROR.value
    if group_config and (not group_config.public and group_config.user_id != user.id):
        log.error(
            f"Group not allowed, user_id: {user.id}, group_id: {payload.group_id}"
        )
        return ErrorKey.INSUFFICIENT_ROLE_LEVEL.value

    if not chat_message_service.verify_input_message(
        payload.message, user.enable_nsfw, user
    ):
        await user_check.add_black_user(user.id)
        return ErrorKey.ILLEGAL_INPUT_BAN.value
    if payload.message and len(payload.message) > 1000:
        log.warning(
            f"Message too long, user_id: {user.id},message_size: {len(payload.message)}"
        )
        return ErrorKey.MESSAGE_TOO_LONG.value
    # message_id = await chat_history_dao.get_last_message_id_by_conv_id(
    #     user.id, payload.conversation_id
    # )
    # if message_id and payload.last_message_id and message_id != payload.last_message_id:
    #     log.warning(
    #         f"Message id not match, user_id: {user.id},conv_id: {payload.conversation_id},db_last_message_id: {message_id},request_last_message_id: {payload.last_message_id}"
    #     )
    # return response_util.chat_error_response(
    # payload.language, ErrorKey.MSG_LOST.value
    #     )
    if payload.message and message_utils.format_and_monitor(payload.message, user):
        log.error(
            f"Message contains system words, user_id: {user.id}, message: {payload.message}"
        )
        return ErrorKey.ILLEGAL_CONTENT.value
    if (role_config and role_config.privacy) or (group_config and group_config.public):
        prohibited_word = message_utils.contain_prohibited_words(payload.message, user)
        if prohibited_word:
            error = _t(ErrorKey.MINOR_SENSITIVE_WORDS.value, payload.language)
            error = error.replace("{{content}}", prohibited_word)
            return error
    return None


async def verify_impersonate_chat(
    user: User, payload: ImpersonateRequest, input: ChatNextInput
):
    request = ChatRequest(
        role_id=payload.role_id,
        group_id=payload.group_id,
        mode_type=payload.mode_type,
        language=payload.language,
        conversation_id=payload.conversation_id,
    )
    error = await verify_chat_str(user, request, input)
    if not error:
        return None
    if error in ErrorKey.all_values():
        error = _t(error, payload.language)
    return response_util.chat_error_message(error)


async def verify_user_input(user: User, payload: ChatRequest, input: ChatNextInput):
    error_key = await verify_chat_str(user, payload, input)
    if not error_key:
        return None

    error = error = (
        _t(error_key, payload.language)
        if error_key in ErrorKey.all_values()
        else error_key
    )
    if error_key == ErrorKey.INSUFFICIENT_BALANCE.value:
        return response_util.error(ErrorCode.INSUFFICIENT_BALANCE.value, error)
    if error_key == ErrorKey.INSUFFICIENT_FREE_BENEFIT.value:
        return None

    # if (
    #     error_key == ErrorKey.INSUFFICIENT_FREE_BENEFIT.value
    #     or error_key == ErrorKey.INSUFFICIENT_BALANCE.value
    # ):
    #     return None
    return response_util.error(ErrorCode.PARAM_ERROR.value, error)


async def verify_chat(user: User, payload: ChatRequest, input: ChatNextInput):
    error = await verify_chat_str(user, payload, input)
    if not error:
        return None
    if error in ErrorKey.all_values():
        error = _t(error, payload.language)
    return response_util.chat_error_message(error)


async def verify_bot_chat_auth(
    user: User, bot: Bot, chat_id: int, payload: ChatRequest, input: ChatNextInput
) -> bool:
    error = await verify_chat_str(user, payload, input)
    if not error:
        return True
    if error == ErrorKey.INSUFFICIENT_BALANCE.value:
        bot_config = await tg_config_service.get_bot_config_by_id(bot.id)
        if bot_config and bot_config.en_bot():
            btn_builder = InlineKeyboardBuilder()
            btn_builder.button(
                text=_tl("Upgrade to Mini App", payload.language),
                url=f'{bot_config.url}/tavern'
            )
            await bot.send_message(chat_id=chat_id,
                text=_t(
                    "Insufficient balance, please recharge first. \n Upgrade to Mini App enjoy the full experience.",
                    payload.language,
                ),
                reply_markup=btn_builder.as_markup()
            )
            return False
        text = await bot_common.chat_bot_charge_tip(user.id)
        total_text = f"余额不足，请先充值！\n{text}"
        button_builder = await bot_common.create_charge_button_builder(bot, user.id)
        await bot.send_message(
            chat_id=chat_id,
            text=total_text,
            parse_mode=ParseMode.HTML,
            reply_markup=button_builder.as_markup(),
        )
        return False
    if error in ErrorKey.all_values():
        processed_error = _t(error, payload.language)
        await bot_message_service.send_by_tips(
            bot=bot, chat_id=chat_id, tips=processed_error, auto_deleted=True
        )
        return False
    await bot_message_service.send_by_tips(
        bot=bot, chat_id=chat_id, tips=error, auto_deleted=True
    )
    return False


# bot直聊时，检查用户选择的聊天模型是否支持该角色：不支持就发送对应切换消息
async def handle_bot_chat_role_support_model_check(
    chat_id: int,
    bot: Bot,
    user: User,
    role: RoleConfig,
    conversation_id: str,
    message_id: str,
    language: str = Language.ZH.value,
):
    model_switch, unsupported_products, supported_products = (
        await check_model_switch_needed(role, user)
    )
    if not model_switch or not model_switch.target_model:
        return None
    user_product = await product_service.get_user_chat_product(user)
    if not user_product:
        return None
    target_product = await product_service.get_chat_product_by_mid(
        model_switch.target_model
    )
    if not target_product:
        return None
    # supported_products 此处不会是None
    supported_product_msg = "、".join(
        [supported_product.model_name for supported_product in supported_products]  # type: ignore
    )
    unsupported_product_msg = "、".join(
        [unsupported_product.model_name for unsupported_product in unsupported_products]  # type: ignore
    )

    regular_msg = bot_common.model_switch_msg.format(
        current_model_name=user_product.display_name,
        unsupported_product_msg=unsupported_product_msg,
        supported_product_msg=supported_product_msg,
    )
    if not model_switch.need_recharge_first:
        # 不需充值有两种情况：该模型是免费模型，或者是付费模型但用户已经充值
        # 直接切换
        user.chat_product_mid = target_product.mid
        await user_service.update_user(user)
        await chat_model_switch_history.add_model_switch_history(
            user.id,
            ChatModeType.SINGLE.value,
            role.id,
            conversation_id,
            message_id,
            ModelEventType.CHAT_AUTO.value,
            user_product.mid,
            user_product.display_name,
            target_product.mid,
            target_product.model_name,
            from_chat_channel=user.chat_channel,
            to_chat_channel=user.chat_channel,
        )
        chat_channel = ChatChannel.safe_parse(user.chat_channel)
        short_display = chat_channel.short_display()
        short_display = f"（{_tl(short_display, language, ChatChannel.__name__)}）"

        messages = [
            _tl("此卡不支持", language),
            _tl(user_product.display_name, language, Product.__name__),
            short_display,
            _tl("，已经自动切换到", language),
            _tl(target_product.model_name, language, Product.__name__),
            short_display,
        ]
        return await bot.send_message(chat_id, "".join(messages))
    else:
        # 目标模型（最低支持的模型）是付费模型，且用户尚未充值
        expensive_all_user_product = (
            await product_service.get_most_expensive_all_user_model()
        )
        # corner case
        if not expensive_all_user_product:
            return await bot.send_message(
                chat_id,
                f"当前角色卡支持：{supported_product_msg}聊天\n1. 更换角色卡，点击更换 /list\n2. 充值后继续聊天，点击去充值 /recharge",
            )

        builder = InlineKeyboardBuilder()
        # 未充值用户当前的免费模型和最贵的免费模型不一样，提示用户切换
        if expensive_all_user_product.mid != user_product.mid:
            builder.button(
                text=f"切换{expensive_all_user_product.model_name}",
                callback_data=bot_common.ModelAutoChangeCallback(
                    mid=expensive_all_user_product.mid
                ),
            )
        builder.button(
            text="最低9.9体验高级模式",
            callback_data=bot_common.RechargeMenuPopupCallback(),
        )
        return await bot.send_message(
            chat_id, regular_msg, reply_markup=builder.as_markup()
        )
