from datetime import datetime, timedelta, UTC
import logging
import uuid

from aiogram.utils.deep_linking import encode_payload
from tortoise.transactions import in_transaction

from common.common_constant import (
    AuthorDefaultName,
    ChatBenefitEnum,
    ChatPlatform,
    Language,
    RoleFilterTag,
)
from persistence import chat_history_dao, redis_client
from persistence.models.models import (
    ExpirableAward,
    ExpirableStatusEnum,
    RechargeChannelEnum,
    RechargeOrder,
    RechargeStatusEnum,
    TelegramUser,
    TgBotConfig,
    User,
    Invitation,
    UserRegisterChannel,
    UserRegisterSource,
    UserStatus,
    UserSummaryStats,
)
from services.account_service import AccountService
from services.user import user_benefit_service
from utils import env_const, json_util, str_util
from utils import translate_util
from utils.translate_util import _t, _tl


log = logging.getLogger(__name__)

INVITATION_REWARD_AMOUNT = 1000
INVITATION_REWARD_EXPIRES = timedelta(days=31)


async def register(email, password):
    user = await User.create(email=email, password=password, nickname=email)
    # 新用户送钻石
    await AccountService.create_account(user.id)
    return user


async def login(email, password):
    user = await User.filter(email=email).first()
    if user and User.verify_password(password, user.password_hash):
        return user
    else:
        return None


async def get_user_by_id(user_id):
    user = await User.get(id=user_id)
    return user


async def safe_get_user(user_id) -> User | None:
    user = await User.filter(id=user_id).first()
    return user


class UserService:

    async def register(self, email, password):
        user = await User.create(email=email, password=password, nickname=email)
        # 新用户送钻石
        await AccountService.create_account(user.id)
        return user

    async def login(self, email, password):
        user = await User.filter(email=email).first()
        if user and User.verify_password(password, user.password_hash):
            return user
        else:
            return None

    async def get_user_by_id(self, user_id):
        user = await User.get(id=user_id)
        return user

    async def safe_get_user(self, user_id) -> User | None:
        user = await User.filter(id=user_id).first()
        return user

    async def update_user(self, user: User):
        await user.save()

    async def get_user_by_tg_id(self, tg_user_id) -> User | None:
        tg_user = await TelegramUser.filter(tg_id=tg_user_id).first()
        if tg_user:
            user = await User.get(id=tg_user.uid)
            return user
        return None

    async def get_users_by_reg_time(self, before_time: datetime) -> list[User]:
        users = await User.filter(created_at__gt=before_time).all()
        return users

    async def get_users_in_one_day(self) -> list[User]:
        now = datetime.now(UTC)
        ab_time = now - timedelta(days=1)
        return await self.get_users_by_reg_time(ab_time)

    async def register_by_tg(
        self,
        tg_user_id,
        first_name,
        last_name,
        user_name,
        register_source: UserRegisterSource = UserRegisterSource.TMA,
        is_premium: bool = False,
        from_bot_id: int = 0,
    ):
        async with in_transaction():
            nsfw_default = User().show_nsfw_image
            if from_bot_id != 0:
                tg_bot_config = await TgBotConfig.filter(bot_id=from_bot_id).first()
                if tg_bot_config and tg_bot_config.en_bot():
                    nsfw_default = False
            nickname = f"{first_name} {last_name}" if last_name else first_name
            user = await User.create(
                email=f"{tg_user_id}@tg",
                password="xx0001",
                nickname=nickname,
                avatar="",
                register_source=register_source,
                show_nsfw_image=nsfw_default,
            )
            tg_user = await TelegramUser.create(
                uid=user.id,
                tg_id=tg_user_id,
                first_name=first_name,
                last_name=last_name or "",
                user_name=user_name or "",
                is_premium=is_premium,
                reg_bot_id=from_bot_id,
            )
            account, is_new = await AccountService.create_account(user.id)
            return user

    async def register_by_tg_with_invite_link(
        self,
        tg_user_id,
        first_name,
        last_name,
        user_name,
        chat_id: int,
        register_source: str,
        invite_link: str | None = None,
        is_premium: bool = False,
        from_bot_id: int = 0,
    ) -> User:
        async with in_transaction():
            nsfw_default = User().show_nsfw_image
            if from_bot_id != 0:
                tg_bot_config = await TgBotConfig.filter(bot_id=from_bot_id).first()
                if tg_bot_config and tg_bot_config.en_bot():
                    nsfw_default = False
            nickname = f"{first_name} {last_name}" if last_name else first_name
            user = await User.create(
                email=f"{tg_user_id}@tg",
                password="xx0001",
                nickname=nickname,
                avatar="",
                register_source=register_source,
                show_nsfw_image=nsfw_default,
            )
            tg_user = await TelegramUser.create(
                uid=user.id,
                tg_id=tg_user_id,
                first_name=first_name,
                last_name=last_name or "",
                user_name=user_name or "",
                is_premium=is_premium,
                reg_bot_id=from_bot_id,
            )
            account, is_new = await AccountService.create_account(user.id)
            if invite_link:
                channel = UserRegisterChannel(
                    user_id=user.id,
                    invite_link=invite_link,
                    chat_id=str(chat_id),
                    joined_chat_type=register_source,
                )
                await channel.save()
            return user

    async def register_by_tg_with_start_role(
        self,
        tg_user_id,
        first_name,
        last_name,
        user_name,
        chat_id: int,
        register_source: str,
        start_role: int = 0,
        is_premium: bool = False,
        from_bot_id: int = 0,
    ) -> User:
        async with in_transaction():
            nsfw_default = User().show_nsfw_image
            if from_bot_id != 0:
                tg_bot_config = await TgBotConfig.filter(bot_id=from_bot_id).first()
                if tg_bot_config and tg_bot_config.en_bot():
                    nsfw_default = False
            nickname = f"{first_name} {last_name}" if last_name else first_name
            user = await User.create(
                email=f"{tg_user_id}@tg",
                password="xx0001",
                nickname=nickname,
                avatar="",
                register_source=register_source,
                show_nsfw_image=nsfw_default,
            )
            tg_user = await TelegramUser.create(
                uid=user.id,
                tg_id=tg_user_id,
                first_name=first_name,
                last_name=last_name or "",
                user_name=user_name or "",
                is_premium=is_premium,
                reg_bot_id=from_bot_id,
            )
            account, is_new = await AccountService.create_account(user.id)

            channel = UserRegisterChannel(
                user_id=user.id,
                chat_id=str(chat_id),
                joined_chat_type=register_source,
                start_role=start_role or 0,
            )
            await channel.save()
            return user

    async def register_tg_with_invitation(
        self,
        tg_user_id,
        first_name,
        last_name,
        user_name,
        inviter_user_id,
        register_source: UserRegisterSource = UserRegisterSource.TMA,
        joint_chat_id: int | None = None,
        start_role: int = 0,
        is_premium: bool = False,
        from_bot_id: int = 0,
    ) -> tuple[bool, User]:
        async with in_transaction():
            nsfw_default = User().show_nsfw_image
            if from_bot_id != 0:
                tg_bot_config = await TgBotConfig.filter(bot_id=from_bot_id).first()
                if tg_bot_config and tg_bot_config.en_bot():
                    nsfw_default = False
            nickname = f"{first_name} {last_name}" if last_name else first_name
            new_user = await User.create(
                email=f"{tg_user_id}@tg",
                password="xx0001",
                nickname=nickname,
                avatar="",
                register_source=register_source,
                show_nsfw_image=nsfw_default,
            )
            tg_user = await TelegramUser.create(
                uid=new_user.id,
                tg_id=tg_user_id,
                first_name=first_name,
                last_name=last_name or "",
                user_name=user_name or "",
                is_premium=is_premium,
                reg_bot_id=from_bot_id,
            )
            await AccountService.create_account(new_user.id)

            invitee_user_id = new_user.id
            current = await Invitation.filter(invitee_user_id=invitee_user_id).first()
            if current is not None:
                return False, current
            invitation = Invitation(
                invitee_user_id=invitee_user_id, inviter_user_id=inviter_user_id
            )
            await invitation.save()

            channel = UserRegisterChannel(
                channel_id=inviter_user_id,
                inviter_user_id=inviter_user_id,
                user_id=invitee_user_id,
                chat_id=str(joint_chat_id or 0),
                joined_chat_type=register_source,
                start_role=start_role or 0,
            )
            await channel.save()
            return True, new_user

    async def get_tg_info_by_user_id(self, user_id) -> TelegramUser | None:
        tg_user = await TelegramUser.filter(uid=user_id).first()
        return tg_user

    async def del_tg_info_by_user_id(self, user_id):
        tg_user = await TelegramUser.filter(uid=user_id).first()
        await tg_user.delete()

    async def add_invitation_reward(self, invitee: User) -> int | None:
        invitee_user_id = invitee.id
        invitation = await Invitation.filter(invitee_user_id=invitee_user_id).first()
        if invitation is None:
            return
        inviter_user_id = invitation.inviter_user_id
        invitation_id = invitation.invitation_id
        recharge_order_id = f"invitation:{invitation_id}"
        award_order = await RechargeOrder.filter(out_order_id=recharge_order_id).first()
        if award_order is not None:
            return

        messages = await chat_history_dao.get_user_history(invitee_user_id, 5)
        human_messages = [m for m in messages if m.type == "human"]
        if len(human_messages) < 2:
            return

        messages_with_cn = [msg for msg in human_messages if len(msg.content) >= 5]
        if len(messages_with_cn) < 2:
            return

        messages_time = [m.timestamp for m in human_messages]
        duration = abs(messages_time[-1] - messages_time[0])
        if duration < 10:
            return

        async with in_transaction():
            now = datetime.now(UTC)
            recharge_order = RechargeOrder(
                user_id=inviter_user_id,
                amount=INVITATION_REWARD_AMOUNT,
                pay_fee=0,
                status=RechargeStatusEnum.SUCCEED,
                recharge_channel=RechargeChannelEnum.INVITATION,
                out_order_id=recharge_order_id,
                finished_at=now,
            )
            await recharge_order.save()

            expirable_award = ExpirableAward(
                user_id=inviter_user_id,
                out_order_id=recharge_order.recharge_order_id,
                total_amount=INVITATION_REWARD_AMOUNT,
                spend_amount=0,
                balance=INVITATION_REWARD_AMOUNT,
                status=ExpirableStatusEnum.NORMAL,
                expires_at=now + INVITATION_REWARD_EXPIRES,
                claim_at=now,
            )
            await expirable_award.save()
            await user_benefit_service.reward_chat_benefit_by_id(
                inviter_user_id, 21, ChatBenefitEnum.INVITATION
            )
            return inviter_user_id

    async def is_payed_user(self, user_id: int) -> bool:
        order_exists = await RechargeOrder.filter(
            user_id=user_id, pay_fee__gt=0, status=RechargeStatusEnum.SUCCEED
        ).exists()
        return order_exists

    async def get_tg_user_by_id(self, user_id):
        tg_user = await TelegramUser.filter(uid=user_id).first()
        return tg_user


user_service = UserService()


async def update_user(user: User):
    await user.save()


async def get_by_id(user_id):
    user = await User.get(id=user_id)
    return user


async def get_by_id_optional(user_id):
    user = await User.filter(id=user_id).first()
    return user


async def get_tg_user_by_id(user_id):
    tg_user = await TelegramUser.filter(uid=user_id).first()
    return tg_user


async def map_nickname(
    user_ids: list[int], language: str = Language.ZH.value
) -> dict[int, str]:
    ret = {}
    anonymous_name = _tl(AuthorDefaultName.ANONYMOUS.value, language)
    if not user_ids:
        ret[0] = anonymous_name
        return ret
    mid_list = await User.filter(id__in=user_ids).only("id", "nickname").all()
    for mid in mid_list:
        ret[mid.id] = mid.nickname
    if language == Language.ZH_TW.value:
        for key in ret:
            ret[key] = translate_util.local_convert(ret[key], Language.ZH_TW.value)
    if language == Language.EN.value:
        for key in ret:
            if not translate_util.check_all_en(ret[key]):
                ret[key] = anonymous_name
    ret[0] = anonymous_name
    return ret


async def map_nickname_avatar(
    user_ids: list[int], language: str = Language.ZH.value
) -> tuple[dict[int, str], dict[int, str]]:
    nickname_dict = {}
    avatar_dict = {}
    nickname_dict[0] = _tl(AuthorDefaultName.ANONYMOUS.value, language)
    avatar_dict[0] = ""

    if not user_ids:
        return nickname_dict, avatar_dict

    mid_list = await User.filter(id__in=user_ids).only("id", "nickname", "avatar").all()
    for mid in mid_list:
        nickname_dict[mid.id] = mid.nickname
        avatar_dict[mid.id] = str_util.format_avatar(mid.avatar)

    return nickname_dict, avatar_dict


async def map_nickname_by_admin(user_ids: list[int]) -> dict[int, str]:
    ret = await map_nickname(user_ids)
    ret[0] = AuthorDefaultName.ADMIN.value
    return ret


async def map_reject_count_by_admin(user_ids: list[int]) -> dict[int, int]:
    ret = {}
    if not user_ids:
        return ret
    mid_list = (
        await User.filter(id__in=user_ids).only("id", "publish_role_privilege").all()
    )
    for mid in mid_list:
        public_privilege = json_util.convert_to_dict(mid.publish_role_privilege)
        if not public_privilege:
            ret[mid.id] = 0
        else:
            ret[mid.id] = public_privilege.get("reject_count", 0)
    return ret


async def get_nickname(
    user_id: int, default_name: str = AuthorDefaultName.ANONYMOUS.value
) -> str:
    maps = await map_nickname([user_id])
    return maps.get(user_id, default_name)


async def is_payed_user(user_id: int) -> bool:
    order_exists = await RechargeOrder.filter(
        user_id=user_id, pay_fee__gt=0, status=RechargeStatusEnum.SUCCEED
    ).exists()
    return order_exists


async def is_author(user_id: int) -> bool:
    user = await User.get(id=user_id)
    return user.status == UserStatus.AUTHOR.value


async def update_user_premium_status(tg_user_id: int, is_premium: bool):
    tg_user = await TelegramUser.filter(tg_id=tg_user_id).first()
    if tg_user is None:
        return
    if tg_user.is_premium == is_premium:
        return
    tg_user.is_premium = is_premium
    return await tg_user.save()


async def get_user_by_tg_id(tg_user_id) -> User | None:
    tg_user = await TelegramUser.filter(tg_id=tg_user_id).first()
    if tg_user:
        user = await User.get(id=tg_user.uid)
        return user
    return None


async def create_tg_login_link(
    tg_user_id: int, language: str = Language.ZH.value
) -> str:
    session_code = str(uuid.uuid4())
    redis_client.redis_client.set(
        f"tg_login:{session_code}", str(tg_user_id), ex=60 * 5
    )
    domain = env_const.WEB_VERSION_URL
    domain = str_util.format_web_domain(domain, language)
    uid_payload = encode_payload(str(tg_user_id))
    url = f"{domain}?uid={uid_payload}&session_code={session_code}"
    return url


async def list_by_admin(start_id: int, limit: int, asc: bool = True):
    if asc:
        return await User.filter(id__gt=start_id).order_by("id").limit(limit).all()
    return await User.filter(id__lt=start_id).order_by("-id").limit(limit).all()


async def max_id_user():
    return await User.all().order_by("-id").first()


async def get_effective_invitations(user_id: int) -> int:
    reward_orders = await RechargeOrder.filter(
        user_id=user_id, recharge_channel=RechargeChannelEnum.INVITATION
    )
    return len(reward_orders)


async def summary_stat(user_id: int):
    user_summary = await UserSummaryStats.filter(user_id=user_id).first()
    if not user_summary:
        user_summary = UserSummaryStats(user_id=user_id)
    return user_summary


async def get_user_rank_type(user_id: int):
    user_summary = await UserSummaryStats.filter(user_id=user_id).first()
    rank_tag = RoleFilterTag.MONTHLY.value
    if not user_summary:
        return rank_tag
    if user_summary and user_summary.activate_days >= 30:
        rank_tag = RoleFilterTag.DAILY.value
    elif user_summary and user_summary.activate_days >= 7:
        rank_tag = RoleFilterTag.WEEKLY.value
    return rank_tag


# 统一一个入口
async def update_chat_channel_and_model(
    user: User, chat_channel: str, chat_product_mid: str, platform: ChatPlatform
):
    user.chat_channel = chat_channel
    user.chat_product_mid = chat_product_mid
    await user.save()
    return user
