from enum import Enum
from typing import Optional
from pydantic import BaseModel

from common.chat_bot_model import AnalysisExampleRet
from common.common_constant import ChatChannel, ChatModeType, ChatApiVersion, Language
from common.models.chat_request import ChatHistoryItem
from common.preset_model import Preset
from common.role_card import BookEntry, CharacterBook
from langchain_core.messages.base import BaseMessage

from persistence.models.models import SbtConfig, UserSbt


class ChatMessage(BaseModel):
    role: str
    content: str
    type: str


class ChatHistoryStatus(int, Enum):
    DELETED = -1


class ChatHistoryType(str, Enum):
    HUMAN = "human"
    AI = "ai"


class HistoryRequest(BaseModel):
    mode_type: str = ChatModeType.SINGLE.value
    role_id: int = 0
    group_id: int = 0
    conversation_id: str = ""
    new_start: int = 0
    language: str = Language.ZH.value

    def verify_param(self):
        if self.mode_type not in ChatModeType.all():
            return False
        if self.mode_type == ChatModeType.SINGLE.value and self.role_id == 0:
            return False
        if self.mode_type == ChatModeType.GROUP.value and self.group_id == 0:
            return False
        return True

    def get_mode_target_id(self):
        if self.mode_type == ChatModeType.SINGLE.value:
            return self.role_id
        else:
            return self.group_id


class BuildHistoryQuery(BaseModel):
    # 基本查询参数
    user_id: int
    nickname: str
    mode_type: str
    mode_target_id: int
    conversation_id: str
    language: str

    request_user_name: str = ""

    # 配置参数
    add_name: bool = False
    regex_option: str = ""
    use_display_user_name: bool = False
    use_request_user_name: bool = False

    # 用户状态栏配置
    usb_switch: bool = True  # 用户状态栏开关
    usb_saved_time: int = 0  # 自定义用户状态栏保存时间
    usb_message_id: str = ""  # 用户状态栏消息ID
    usb_message_version: int = 0  # 用户状态栏消息版本


# key(user_id,mode_type,mode_target_id)
class ChatHistory(BaseModel):
    user_id: int = 0
    mode_type: str = ChatModeType.SINGLE.value
    mode_target_id: int = 0  # role_id or group_id
    role_id: int = 0
    content: str = ""
    conversation_id: str = ""  # 会话ID
    message_id: str = ""  # 消息ID
    type: str = ""  # 消息类型
    timestamp: int = 0  # 时间戳
    version: int = 0  # 版本
    input_token_count: int = 0
    output_token_count: int = 0
    voice_url: str = ""
    model: str = ""
    original_model: str = ""
    duration: float = 0.0
    platform: str = "TMA"  # 默认为 Telegram Mini App
    photo_url: str = ""
    photo_id: str = ""
    retry_photos: list[dict] = []  # {photo_id, photo_url}重试的图片列表
    chat_continue: bool = False
    language: str = ""
    chat_product_mid: str = ""
    product_id: str = ""
    select_water_model: str = ""  # 选择的掺水模型


class RecentChatSimple(BaseModel):
    mode_type: str
    mode_target_id: int
    timestamp: int
    record_count: int = 0


class ChatTipsHistory(BaseModel):
    user_id: int
    role_id: int
    content: str
    conversation_id: str
    timestamp: int
    input_token_count: int = 0
    output_token_count: int = 0
    model: Optional[str] = None
    original_model: Optional[str] = None
    chat_product_mid: Optional[str] = None
    select_water_model: Optional[str] = None


# 按照优先级初步增加参数
class ChatNextInput(BaseModel):
    # user input
    mode_type: str = ChatModeType.SINGLE.value
    group_id: int = 0
    role_id: int = 0
    input_content: str = ""
    conversation_id: str = ""
    isRetry: bool = False
    auto_retry: bool = False
    retry_message_id: str = ""
    language: str = Language.ZH.value
    input_last_message_id: str = ""
    input_last_message_version: str = ""
    api_version: str = ChatApiVersion.V1.value  # API版本
    platform: str = "TMA"  # 平台，默认为 Telegram Mini App

    message_id: str = ""
    human_message_id: str = ""
    version: int = 0
    chat_continue: bool = False
    chat_scenario: int = 1  # 聊天场景

    # 续写功能
    chat_continue_ai_msg: str = ""
    chat_continue_message_id: str = ""

    # user config
    nickname: str = ""
    user_id: int = 0
    user_status: int = 0
    llm_model: str = ""
    user_chat_product_mid: str = ""
    register_source: str = ""
    balance: int = 0
    payed_balance: int = 0
    chat_free_benefit: int = 0
    user_model_filter: str = ""  # 用户模型过滤器
    chat_channel: str = ChatChannel.PAID.value
    usb_switch: bool = True  # 用户状态栏开关
    usb_saved_time: int = 0  # 用户状态栏保存时间
    usb_message_id: str = ""  # 用户状态栏消息ID
    usb_message_version: int = 0  # 用户状态栏消息版本
    usb_rule: str = ""  # 用户状态栏规则
    usb_template_content: str = ""  # 用户状态栏模板内容

    # role config, data_config: RoleDataConfig = {}
    privacy: bool = False
    status: int = 0
    nsfw: bool = False
    role_created_uid: int = 0
    role_name: str = ""
    role_chat_type: str = ""
    user_role_name: str = ""
    request_user_name: str = ""
    display_user_name: str = ""
    # replay_max_count: int = 0
    replay_len_ratio: int = 0
    description: str = ""
    personality: str = ""
    user_personality: str = ""
    scenario: str = ""
    format_example: Optional[AnalysisExampleRet] = None
    role_default_language: str = Language.ZH.value
    first_message: str = ""

    status_block_enable: bool = False
    status_block_type: str = ""
    status_block: str = ""  # {{StatusBar}}
    status_rules: str = ""  # {{StatusRules}}

    book_id: str = ""

    # user chat config
    history: list[ChatHistoryItem] = []
    first_ai_message: str = ""
    last_user_message: str = ""
    last_message_id: int = 0

    # model config and system config
    select_water_model: str = ""  # 选择的掺水模型
    preset_model: str = ""  # 选择的模型
    request_model: str = ""  # 实际请求的模型
    preset: Optional[Preset] = None
    preset_dict: dict = {}
    llm_model_support_params: list[str] = []  # 请求模型支持参数列表
    llm_request_cluster: str = ""  # 请求模型集群
    llm_request_base_url: str = ""  # 请求模型基础地址
    add_water: bool = False  # 是否需要添加水
    out_skip_count: int = 0  # 输出跳过次数
    out_sleep_min: float = 0  # 输出跳过最小时间
    out_sleep_max: float = 0  # 输出跳过最大时间
    add_name_prefix: bool = True  # 是否需要添加名字前缀
    deal_regex_rule: bool = True  # 是否需要处理正则规则
    copywriting: bool = False  # 是否需要处理copywriting
    product_display_name: str = ""
    use_cache: bool = False  # 是否使用缓存

    # other
    regex_rules: list = []

    # character book
    character_book: Optional[CharacterBook] = None
    world_info_before: str = ""
    world_info_after: str = ""
    constant_book_entries: list[BookEntry] = []
    keywords_book_entries: list[BookEntry] = []

    # output params
    output_interrupt: bool = False
    finish_reason: str = ""
    llm_call_id: str = ""
    save_finished: bool = False  # 是否保存完成的消息

    # 时间跟踪
    timestamp_start: int = 0
    timestamp_lite_req_start: int = 0
    timestamp_first_token: int = 0
    timestamp_end: int = 0

    def get_headers(self):
        return {
            "Conversation-Id": self.conversation_id,
            "Message-Id": self.message_id,
            "Human-Message-Id": self.human_message_id,
            "Message-Version": str(self.version),
        }

    def time_data(self):
        sr = self.timestamp_lite_req_start - self.timestamp_start
        sf = self.timestamp_first_token - self.timestamp_lite_req_start
        fe = self.timestamp_end - self.timestamp_first_token
        time_logs = [
            f"start->req_s:{max(0, sr)}s",
            f"req_s->ft:{max(0, sf)}s",
            f"ft-end:{max(0, fe)}s",
        ]
        return ",".join(time_logs)


class AnalysisUserHistory(BaseModel):
    history: list[dict]
    conversation_id: str = ""
    message_id: str = ""
    human_message_id: str = ""
    first_ai_message: str = ""
    ai_models: list[str] = []


class SelectModelRet(BaseModel):
    valid_balance: bool
    user_model: str  # 用户选择的模型
    preset_model: str  # 选择的模型
    request_model: str  # 实际请求的模型
    add_water: bool = False  # 是否需要添加水
    model_name: str = ""  # 模型名称


class SelectUserRoleName(BaseModel):
    # 用户名称
    nickname: str = ""
    user_role_name: str = ""

    request_user_name: str = ""
    display_user_name: str = ""


class ChatBotStateData(BaseModel):
    role_id: Optional[int] = 0
    last_mids: Optional[list[int]] = []
    conversation_id: Optional[str] = ""
    op_noticed: Optional[bool] = None
    pay_type: Optional[str] = None
    # 新用户引导
    new_user_guide: Optional[bool] = None

    def chat_verify(self):
        if not self.role_id:
            return False
        if not self.conversation_id:
            return False
        return True


class ChatFinishData(BaseModel):
    success: bool = False
    conversation_id: str = ""
    message_id: str = ""
    human_message_id: str = ""
    message_version: str = ""
    finish_reason: str = "stop"
    need_retry: bool = False
    error: str = ""


class AddWaterMid(BaseModel):
    add_water: bool = False
    llm_model: str = ""
    preset_model: str = ""
    config_skip_count: int = 0
    config_sleep_min: float = 0
    config_sleep_max: float = 0

    # sleep时间
    current_count: int = 0
    sleep_count: int = 0

    # skip输出
    skip_output: bool = False
    skip_count: int = 0


class UcbBrief(BaseModel):
    sum_remain_times: int = 0
    sum_reward_times: int = 0
    reward_times: int = 0
    product_short_name: str = ""
    product_mid: str = ""


class UserChatBenefitDetail(BaseModel):
    # 剩余次数
    remain_times: int = 0
    # 总次数
    reward_times: int = 0
    benefit_title: str = ""

    # 过期时间
    expire_at: str = ""

    # 下次领取时间
    next_receive_at: str = ""

    model_display_name: str = ""
    model_mid: str = ""


class UserChatBalanceResponse(BaseModel):
    payed_balance: int = 0
    reward_balance: int = 0
    benefits: dict[str, UcbBrief] = {}
    payed_user:bool = False  # 是否为付费用户


class ModelStatus(Enum):
    # 不可用
    UNAVAILABLE = -1
    # 拥堵
    CONGESTION = 1
    # 可用
    PARTIALLY_AVAILABLE = 10
    # 通畅
    AVAILABLE = 200

    @staticmethod
    def to_desc(status: int):
        desc_map = {
            ModelStatus.UNAVAILABLE.value: "不可用",
            ModelStatus.CONGESTION.value: "拥堵",
            ModelStatus.PARTIALLY_AVAILABLE.value: "可用",
            ModelStatus.AVAILABLE.value: "通畅",
        }
        return desc_map.get(status, "未知状态")


# 模型统计
class ModelLlmRequestStat(BaseModel):
    model: str = ""
    success_count: int = 0
    fail_count: int = 0
    total_count: int = 0

    def format_status(self):
        if self.total_count == 0:
            return ModelStatus.AVAILABLE
        if self.total_count > 0 and self.success_count == 0:
            return ModelStatus.UNAVAILABLE
        if self.success_count / self.total_count > 0.95:
            return ModelStatus.AVAILABLE
        if self.success_count / self.total_count > 0.75:
            return ModelStatus.PARTIALLY_AVAILABLE
        if self.success_count / self.total_count > 0.5:
            return ModelStatus.CONGESTION
        return ModelStatus.UNAVAILABLE


class LiteLlmIterResponse(BaseModel):
    # 迭代器返回的内容
    response: str = ""
    finish_reason: str = ""
    usage: bool = False
    completion_tokens: int = 0
    prompt_tokens: int = 0
    cache_created_tokens: int = 0
    cache_read_tokens: int = 0


# =========== status block template ===========
# 生成状态栏请求
class GenerateSbtRequest(BaseModel):
    sbt_id: int  # 模板ID
    conversation_id: str  # 回合ID
    message_id: str  # 消息ID
    version: int  # 消息版本号


# 保存状态栏请求
class SaveUserSbtRequest(BaseModel):
    conversation_id: str  # 回合ID
    message_id: str = ""  # 消息ID
    version: int = 0  # 消息版本号
    content: str = ""  # 生成内容
    rule: str = ""  # 规则
    sbt_id: int = 0  # 模板ID(自定义)
    ai_generate: bool = False  # 是否AI编辑（用户填写或者编辑，传递True）


# 状态栏模版详情
class SbtItem(BaseModel):
    prefix: str = ""  # 模板前缀
    placeholder: str = ""  # 模板占位符


class SbtDetail(BaseModel):
    id: int  # 模板ID
    title: str  # 模板标题
    content: str = ""  # 模板内容
    content_placeholder: str = ""  # 模板内容占位符
    rule: str = ""  # 模板规则

    @staticmethod
    def from_model(model: SbtConfig):
        return SbtDetail(
            id=model.id,
            title=model.title,
            content=model.content,
            content_placeholder=model.content_placeholder,
            rule=model.rule,
        )


# 用户设置的状态栏详情
class UserSbtDetail(BaseModel):
    id: int = 0  # 用户状态栏ID
    conversation_id: str = ""  # 回合ID
    content: str = ""  # 生成内容
    rule: str = ""  # 规则
    last_saved_time: int = 0  # 最后保存时间

    template_content: str = ""  # 内容模板
    sbt_id: int = 0  # 模板ID
    message_id: str = ""  # 消息ID
    message_version: int = 0  # 消息版本号

    @staticmethod
    def from_model(user_sbt: UserSbt, sbt_config: SbtConfig | None) -> "UserSbtDetail":
        return UserSbtDetail(
            id=user_sbt.id,
            conversation_id=user_sbt.conversation_id,
            content=user_sbt.content,
            rule=(
                user_sbt.rule
                if user_sbt.rule
                else sbt_config.rule if sbt_config else ""
            ),
            last_saved_time=user_sbt.last_saved_time,
            template_content=sbt_config.content if sbt_config else "",
            sbt_id=sbt_config.id if sbt_config else 0,
            message_id=user_sbt.message_id or "",
            message_version=user_sbt.message_version or 0,
        )
